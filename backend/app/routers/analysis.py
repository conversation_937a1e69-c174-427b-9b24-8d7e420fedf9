"""
Analysis router for CaseBuilder AI.
Handles document analysis requests and results.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
import logging
import uuid
import time
from typing import List, Dict, Any
import asyncio
import aiohttp
import base64
import io
import os

from ..models.requests import AnalysisRequest
from ..models.responses import (
    AnalysisResponse,
    AnalysisListResponse,
    BaseResponse,
    ProgressResponse
)
from ..models.preferences import AnalysisType
from pydantic import BaseModel
from typing import List
from ..services.auth_service import User
from ..dependencies import get_current_user, get_current_session, validate_session_access
from ..middleware.session import session_store

logger = logging.getLogger(__name__)
router = APIRouter()


# Direct analysis request model
class DirectAnalysisFile(BaseModel):
    name: str
    content: str  # base64 content
    type: str


class DirectAnalysisRequest(BaseModel):
    files: List[DirectAnalysisFile]
    preferences: dict
    analysis_type: str


@router.post("/start", response_model=AnalysisResponse)
async def start_analysis(
    analysis_request: AnalysisRequest,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Start document analysis with specified preferences.
    
    Args:
        analysis_request: Analysis configuration
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        AnalysisResponse with analysis details
    """
    try:
        session_id = getattr(request.state, "session_id")
        session_data = await get_current_session(request)

        # Validate that documents exist
        documents = session_data.get("documents", {})
        for doc_id in analysis_request.document_ids:
            if doc_id not in documents:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Document {doc_id} not found"
                )
        
        # Generate analysis ID
        analysis_id = str(uuid.uuid4())
        current_time = time.time()
        
        # Create analysis record
        analysis_data = {
            "id": analysis_id,
            "document_ids": analysis_request.document_ids,
            "preferences": analysis_request.preferences.model_dump(),
            "selected_analyses": [a.value for a in analysis_request.selected_analyses],
            "status": "started",
            "progress": 0,
            "results": {},
            "started_at": current_time,
            "completed_at": None,
            "error": None
        }
        
        # Store analysis in session
        analyses = session_data.get("analyses", {})
        analyses[analysis_id] = analysis_data
        
        session_store.update_session(session_id, {
            "analyses": analyses
        })
        
        # Start background analysis
        asyncio.create_task(
            process_analysis_background(analysis_id, session_id, analysis_request)
        )
        
        logger.info(f"Analysis {analysis_id} started by user {user.username}")

        return AnalysisResponse(
            analysis_id=analysis_id,
            analysis_type="multi_analysis",
            status="started",
            progress=0,
            started_at=current_time,
            message="Analysis started successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting analysis for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start analysis"
        )


@router.get("/", response_model=AnalysisListResponse)
async def list_analyses(
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    List all analyses in the current session.
    
    Args:
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        AnalysisListResponse with list of analyses
    """
    try:
        session_data = await get_current_session(request)
        analyses = session_data.get("analyses", {})
        
        # Convert to response format
        analysis_list = []
        for analysis_id, analysis_data in analyses.items():
            analysis_list.append(AnalysisResponse(
                analysis_id=analysis_id,
                analysis_type="multi_analysis",
                status=analysis_data["status"],
                progress=analysis_data["progress"],
                started_at=analysis_data["started_at"],
                completed_at=analysis_data.get("completed_at")
            ))
        
        return AnalysisListResponse(
            analyses=analysis_list,
            total_count=len(analysis_list),
            message="Analyses retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error listing analyses for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve analyses"
        )


@router.get("/{analysis_id}", response_model=AnalysisResponse)
async def get_analysis(
    analysis_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Get analysis details and results by ID.
    
    Args:
        analysis_id: Analysis identifier
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        AnalysisResponse with analysis details and results
    """
    try:
        session_data = await get_current_session(request)
        analyses = session_data.get("analyses", {})
        
        if analysis_id not in analyses:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )
        
        analysis_data = analyses[analysis_id]
        
        # Get results if completed
        result = None
        if analysis_data["status"] == "completed" and analysis_data["results"]:
            # Combine all results into a single string for now
            # In a real implementation, this would be more structured
            result_parts = []
            for analysis_type, analysis_result in analysis_data["results"].items():
                result_parts.append(f"=== {analysis_type.upper()} ===\n{analysis_result}\n")
            result = "\n".join(result_parts)
        
        return AnalysisResponse(
            analysis_id=analysis_id,
            analysis_type="multi_analysis",
            status=analysis_data["status"],
            result=result,
            progress=analysis_data["progress"],
            started_at=analysis_data["started_at"],
            completed_at=analysis_data.get("completed_at"),
            message="Analysis retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting analysis {analysis_id} for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve analysis"
        )


@router.get("/{analysis_id}/progress", response_model=ProgressResponse)
async def get_analysis_progress(
    analysis_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Get analysis progress.
    
    Args:
        analysis_id: Analysis identifier
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        ProgressResponse with analysis progress
    """
    try:
        session_data = await get_current_session(request)
        analyses = session_data.get("analyses", {})
        
        if analysis_id not in analyses:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )
        
        analysis_data = analyses[analysis_id]
        
        return ProgressResponse(
            task_id=analysis_id,
            status=analysis_data["status"],
            progress=analysis_data["progress"],
            message=f"Analysis {analysis_data['status']}",
            started_at=analysis_data["started_at"],
            updated_at=time.time()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting progress for analysis {analysis_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get analysis progress"
        )


@router.delete("/{analysis_id}", response_model=BaseResponse)
async def cancel_analysis(
    analysis_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Cancel or delete an analysis.
    
    Args:
        analysis_id: Analysis identifier
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        BaseResponse confirming cancellation
    """
    try:
        session_id = getattr(request.state, "session_id")
        session_data = await get_current_session(request)
        analyses = session_data.get("analyses", {})
        
        if analysis_id not in analyses:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )
        
        # Remove analysis from session
        del analyses[analysis_id]
        
        session_store.update_session(session_id, {
            "analyses": analyses
        })
        
        logger.info(f"Analysis {analysis_id} cancelled by user {user.username}")
        
        return BaseResponse(message="Analysis cancelled successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling analysis {analysis_id} for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel analysis"
        )


@router.post("/direct")
async def direct_analysis(
    request_data: DirectAnalysisRequest
    # Temporarily remove authentication for testing
    # user: User = Depends(get_current_user)
):
    """
    Direct analysis endpoint that processes files from browser memory.
    Uses the existing CaseBuilder StreamlitAdapter.
    """
    try:
        logger.info(f"Starting direct analysis with {len(request_data.files)} files")

        # Use OpenAI directly with the exact same prompt as CaseBuilder
        from openai import AsyncOpenAI
        import os

        client = AsyncOpenAI(api_key=os.getenv('OPENAI_API_KEY'))

        logger.info("OpenAI client setup successful")

        # Extract text from files (simplified for testing)
        combined_text = []
        for file_data in request_data.files:
            try:
                logger.info(f"Processing file: {file_data.name}, type: {file_data.type}")

                # Decode base64 content
                import base64
                try:
                    # Handle data URLs (data:type;base64,content)
                    if ',' in file_data.content:
                        file_content = base64.b64decode(file_data.content.split(',')[1])
                    else:
                        file_content = base64.b64decode(file_data.content)

                    logger.info(f"Decoded {len(file_content)} bytes from {file_data.name}")
                except Exception as decode_error:
                    logger.error(f"Base64 decode error for {file_data.name}: {str(decode_error)}")
                    continue

                # Extract text based on file type
                text = ""
                if file_data.type == "application/pdf":
                    # PDF text extraction with detailed logging
                    try:
                        from PyPDF2 import PdfReader
                        from io import BytesIO
                        pdf_file = BytesIO(file_content)
                        pdf_reader = PdfReader(pdf_file)
                        logger.info(f"PDF {file_data.name} has {len(pdf_reader.pages)} pages")

                        text_parts = []
                        for i, page in enumerate(pdf_reader.pages):
                            try:
                                page_text = page.extract_text()
                                logger.info(f"Page {i+1}: extracted {len(page_text)} characters")
                                if page_text.strip():
                                    text_parts.append(page_text)
                                    logger.info(f"Page {i+1}: added {len(page_text.strip())} non-empty characters")
                                else:
                                    logger.warning(f"Page {i+1}: no text content found")
                            except Exception as page_error:
                                logger.error(f"Error extracting text from page {i+1}: {str(page_error)}")

                        text = "\n".join(text_parts)
                        logger.info(f"Total extracted {len(text)} characters from PDF {file_data.name}")

                        # If no text extracted, try OCR with GPT-4o Vision
                        if not text.strip():
                            logger.warning(f"No text extracted from PDF {file_data.name}, trying OCR with GPT-4o Vision")
                            try:
                                # Convert PDF to images and use OCR
                                from pdf2image import convert_from_bytes
                                import base64

                                # Convert PDF to images with optimized settings
                                images = convert_from_bytes(
                                    file_content,
                                    dpi=150,  # Higher DPI for better OCR accuracy
                                    thread_count=2,  # Reduce threads to avoid memory issues
                                    use_cropbox=True,
                                    grayscale=True
                                )

                                # Limit pages for faster processing (first 5 pages for testing)
                                max_pages = min(5, len(images))
                                images = images[:max_pages]
                                logger.info(f"Processing {len(images)} pages (limited to {max_pages}) for OCR")

                                # Process pages in parallel with limited concurrency
                                async def process_page_ocr(i, image):
                                    try:
                                        # Convert PIL image to bytes
                                        img_byte_arr = io.BytesIO()
                                        image.save(img_byte_arr, format='PNG', optimize=True)
                                        img_bytes = img_byte_arr.getvalue()

                                        # Encode to base64
                                        base64_image = base64.b64encode(img_bytes).decode('utf-8')

                                        # Call GPT-4o Vision for OCR
                                        ocr_payload = {
                                            "model": "gpt-4o-2024-05-13",
                                            "messages": [
                                                {
                                                    "role": "user",
                                                    "content": [
                                                        {
                                                            "type": "text",
                                                            "text": "Extract all text from this document page. Maintain structure and formatting. Return only the extracted text."
                                                        },
                                                        {
                                                            "type": "image_url",
                                                            "image_url": {
                                                                "url": f"data:image/png;base64,{base64_image}"
                                                            }
                                                        }
                                                    ]
                                                }
                                            ],
                                            "max_tokens": 3000  # Reduced for faster processing
                                        }

                                        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
                                            async with session.post(
                                                "https://api.openai.com/v1/chat/completions",
                                                headers={
                                                    "Content-Type": "application/json",
                                                    "Authorization": f"Bearer {os.getenv('OPENAI_API_KEY')}"
                                                },
                                                json=ocr_payload
                                            ) as response:
                                                if response.status == 200:
                                                    response_json = await response.json()
                                                    choices = response_json.get('choices', [])
                                                    if choices:
                                                        page_text = choices[0]['message']['content']
                                                        if page_text.strip():
                                                            logger.info(f"OCR extracted {len(page_text)} characters from page {i+1}")
                                                            return f"=== Page {i+1} ===\n{page_text}"
                                                        else:
                                                            logger.warning(f"OCR found no text on page {i+1}")
                                                            return None
                                                    else:
                                                        logger.warning(f"No OCR response for page {i+1}")
                                                        return None
                                                else:
                                                    logger.error(f"OCR API error for page {i+1}: {response.status}")
                                                    return None
                                    except Exception as page_ocr_error:
                                        logger.error(f"OCR error for page {i+1}: {str(page_ocr_error)}")
                                        return None

                                # Process pages with limited concurrency (2 at a time)
                                semaphore = asyncio.Semaphore(2)

                                async def process_with_semaphore(i, image):
                                    async with semaphore:
                                        return await process_page_ocr(i, image)

                                # Create tasks for all pages
                                tasks = [process_with_semaphore(i, image) for i, image in enumerate(images)]

                                # Wait for all tasks to complete
                                ocr_results = await asyncio.gather(*tasks, return_exceptions=True)

                                # Filter successful results
                                ocr_text_parts = [result for result in ocr_results if isinstance(result, str) and result]

                                if ocr_text_parts:
                                    text = "\n\n".join(ocr_text_parts)
                                    logger.info(f"OCR extracted total {len(text)} characters from PDF {file_data.name}")
                                else:
                                    text = f"[PDF {file_data.name} - both text extraction and OCR failed]"
                                    logger.error(f"Both text extraction and OCR failed for {file_data.name}")

                            except Exception as ocr_error:
                                logger.error(f"OCR processing error for {file_data.name}: {str(ocr_error)}")
                                text = f"[PDF {file_data.name} - text extraction failed, OCR error: {str(ocr_error)}]"

                    except Exception as pdf_error:
                        logger.error(f"PDF extraction error for {file_data.name}: {str(pdf_error)}")
                        text = f"Error extracting PDF text: {str(pdf_error)}"

                elif file_data.type.startswith("text/") or file_data.name.endswith('.txt'):
                    # Text file
                    try:
                        text = file_content.decode('utf-8')
                        logger.info(f"Extracted {len(text)} characters from text file {file_data.name}")
                    except UnicodeDecodeError:
                        try:
                            text = file_content.decode('latin-1')
                            logger.info(f"Extracted {len(text)} characters from text file {file_data.name} (latin-1)")
                        except Exception as text_error:
                            logger.error(f"Text decode error for {file_data.name}: {str(text_error)}")
                            text = f"Could not decode text from {file_data.name}"
                else:
                    # Unknown file type - try as text
                    try:
                        text = file_content.decode('utf-8')
                        logger.info(f"Extracted {len(text)} characters from unknown type {file_data.name}")
                    except:
                        text = f"Unsupported file type: {file_data.type}"

                if text.strip():
                    combined_text.append(f"=== {file_data.name} ===\n{text}\n")
                    logger.info(f"Added text from {file_data.name} to combined text")
                else:
                    logger.warning(f"No text content found in {file_data.name}")

            except Exception as e:
                logger.error(f"Error processing file {file_data.name}: {str(e)}")
                combined_text.append(f"=== {file_data.name} ===\nError processing file: {str(e)}\n")

        if not combined_text:
            logger.warning("No text content extracted from files")
            return {"error": "No text content could be extracted from the uploaded files"}

        document_text = "\n".join(combined_text)
        logger.info(f"Extracted text length: {len(document_text)} characters")

        # Process analysis using exact CaseBuilder prompt
        if request_data.analysis_type == 'police_report_summary':
            logger.info("Processing police report summary using exact CaseBuilder prompt")

            try:
                # Get preferences
                prefs = request_data.preferences or {}
                client_name = prefs.get('client_name', 'the client')
                additional_notes = prefs.get('additional_notes', '')

                # Use the EXACT prompt from CaseBuilder FactsOfLossHandler
                prompt = f"""
                You are an AI legal expert specializing in personal injury cases.
                Your task is to produce a clear, concise summary of the police report for a traffic collision.

                Provide a balanced analysis with essential details about the accident and liability determination.

                Extract and summarize the following key information:
                1. Date, time, and location of the accident
                2. Parties involved (vehicles, drivers, passengers)
                3. Brief description of how the accident occurred
                4. Weather and road conditions
                5. Reported injuries (if any)
                6. Citations issued (if any)
                7. Officer's determination of fault/liability (if stated in the report)

                For the liability determination (#7), include the officer's opinion if it's stated in the report, but clearly label it as "Officer's Opinion" and add this note: "Note: The officer's opinion on fault is not a legal determination and may not reflect the actual legal liability in this case."
                This is just a factual summary of what's in the report, including the officer's stated conclusions.

                Format your response in a structured, easy-to-read format with clear headings.
                Keep your summary brief and focused on the most important details.

                **Additional Notes (if applicable):**
                {additional_notes}
                """

                # Call OpenAI with exact same structure as CaseBuilder
                response = await client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": "You are an AI legal expert specializing in personal injury cases."},
                        {"role": "user", "content": f"{prompt}\n\nPolice Report Content:\n{document_text}"}
                    ],
                    max_tokens=4000,
                    temperature=0.3
                )

                result = response.choices[0].message.content
                logger.info(f"OpenAI analysis completed: {len(result)} characters")

            except Exception as e:
                logger.error(f"OpenAI error: {str(e)}")
                result = f"Error generating analysis: {str(e)}"

        elif request_data.analysis_type == 'in_depth_liability':
            logger.info("Processing in-depth liability analysis using exact CaseBuilder prompt")

            try:
                # Get preferences
                prefs = request_data.preferences or {}
                client_name = prefs.get('client_name', 'the client')
                additional_notes = prefs.get('additional_notes', '')

                # Use the EXACT prompt from CaseBuilder DetermineLiabilityHandler with in_depth=True
                prompt = f"""
                You are an AI legal expert specializing in traffic accident liability analysis.

                Conduct a comprehensive, in-depth liability analysis of the provided police report for {client_name}'s case.

                CRITICAL INSTRUCTION: You MUST COMPLETELY IGNORE the officer's opinion on fault or liability. Police officers are not legal experts and their opinions on fault are often incorrect or biased. Your task is to determine liability based SOLELY on the factual evidence and applicable traffic laws.

                IMPORTANT LEGAL PRINCIPLES:
                - In pedestrian-vehicle accidents, drivers have a heightened duty of care toward pedestrians.
                - A pedestrian in a crosswalk with a walk signal has absolute right of way.
                - Drivers must yield to pedestrians in crosswalks, even if the pedestrian entered unexpectedly.
                - Comparative negligence should ONLY be applied when there is CLEAR evidence the pedestrian violated traffic laws.
                - The fact that a collision occurred is itself evidence that the driver failed to maintain proper control of their vehicle.

                Your analysis should focus on determining who is at fault for the accident, with these components:

                1. **Factual Sequence Analysis**: Analyze the sequence of events leading to the accident based on the factual evidence only. Do not reference or consider the officer's opinion at any point. Apply the correct legal standards based on the type of accident (vehicle-vehicle, vehicle-pedestrian, etc.).

                2. **Witness Statement Evaluation**: Critically evaluate witness statements for reliability and what they reveal about fault. IMPORTANT: A witness is defined as any third-party observer who is NOT directly involved in the accident (not the drivers or passengers of the vehicles involved). Only if the witness statements are unreliable, mention this fact.

                3. **Traffic Law Application**: Apply relevant traffic laws to determine which party violated applicable regulations. Be specific about which laws were violated and how they apply to this case. Remember that:
                   - Drivers must maintain control of their vehicle at all times
                   - Drivers must maintain a safe following distance
                   - Drivers must yield right of way according to traffic laws
                   - Drivers must exercise extra caution around pedestrians, bicyclists, and in school zones
                   - A driver's duty to avoid a collision supersedes most other considerations

                4. **Physical Evidence Assessment**: Analyze vehicle damage patterns, road conditions, and other physical evidence that indicates fault.

                5. **Primary Fault Determination**: Identify which party bears the primary responsibility for the accident.

                   IMPORTANT: Do NOT assign shared responsibility or comparative negligence unless there is CLEAR AND CONVINCING evidence that both parties violated specific traffic laws. The mere occurrence of an accident is not evidence of shared fault. In cases where one party had the right of way (especially pedestrians in crosswalks), that party should almost never be assigned any portion of fault.

                   If you do find clear evidence of shared responsibility, mention this fact without assigning specific percentages.

                6. **Evidence Strength Assessment**: Rate the strength of evidence supporting your liability determination (strong, moderate, or weak). Only if the evidence is weak, mention this fact.

                7. **Liability Conclusion**: Provide your final determination of liability with clear reasoning, focusing on who is legally responsible for the accident.

                The primary purpose of this analysis is NOT to provide a detailed narrative, but to establish WHO IS AT FAULT based on a thorough analysis of the evidence.

                Format your analysis in a structured, professional manner suitable for legal review.

                FORMATTING REQUIREMENTS:
                - Use minimal markdown formatting (avoid **, ##, ###, etc.)
                - Keep formatting clean and copy-paste friendly
                - Avoid excessive headers, subheaders, or nested formatting
                - Use simple text with minimal bold formatting only when absolutely necessary

                Additional Notes (if applicable):
                {additional_notes}
                """

                # Call OpenAI with exact same structure as CaseBuilder
                response = await client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": "You are an AI legal expert specializing in traffic accident liability analysis."},
                        {"role": "user", "content": f"{prompt}\n\nPolice Report Content:\n{document_text}"}
                    ],
                    max_tokens=4000,
                    temperature=0.3
                )

                result = response.choices[0].message.content
                logger.info(f"OpenAI in-depth liability analysis completed: {len(result)} characters")

            except Exception as e:
                logger.error(f"OpenAI error: {str(e)}")
                result = f"Error generating analysis: {str(e)}"

        elif request_data.analysis_type == 'medical_analysis':
            logger.info("Processing medical analysis using exact CaseBuilder prompt")

            try:
                # Get preferences
                prefs = request_data.preferences or {}
                client_name = prefs.get('client_name', 'the client')
                additional_notes = prefs.get('additional_notes', '')

                # Get analysis configuration from preferences
                analysis_config = prefs.get('analysis', {})
                detail_level = analysis_config.get('detail_level', 'standard').lower()
                content_emphasis = analysis_config.get('content_emphasis', 'medical').lower()
                analysis_style = analysis_config.get('analysis_style', 'narrative').lower()

                # Map frontend values to backend values
                style_mapping = {
                    'narrative': 'narrative',
                    'chronological': 'chronological',
                    'bulleted': 'bulleted',
                    'tabular': 'tabular'
                }
                style = style_mapping.get(analysis_style, 'narrative')

                detail_mapping = {
                    'basic': 'brief',
                    'standard': 'moderate',
                    'comprehensive': 'comprehensive'
                }
                detail = detail_mapping.get(detail_level, 'moderate')

                # Create style instructions based on selected style
                style_instructions = {
                    "narrative": "Present the medical analysis in a flowing narrative format that tells the story of the client's medical journey. Use paragraphs to organize information and create a cohesive story. IMPORTANT: Do NOT use bullet points, numbered lists, or markdown formatting. Write in complete sentences and paragraphs as if writing a story or essay. Avoid using headers or section titles - integrate all information into a continuous narrative.",
                    "chronological": "Organize the medical analysis in chronological order, listing each medical event by date with associated details.",
                    "bulleted": "Present the medical analysis as bulleted points for clarity, grouping by provider or category as needed.",
                    "tabular": "Present the medical analysis in a structured table format where appropriate."
                }.get(style, "Present the medical analysis in a flowing narrative format.")

                # Create detail level instructions
                detail_instructions = {
                    "brief": "Provide a concise summary of the most important medical information. Focus only on key injuries, major treatments, and significant findings. Omit minor details and routine visits.",
                    "moderate": "Provide a balanced analysis with essential details. Include all significant medical events and treatments while summarizing routine care. Focus on information relevant to the case.",
                    "comprehensive": "Provide an exhaustive analysis with all available medical details. Include every treatment, visit, medication, and finding from the records. Be thorough and complete in your analysis."
                }.get(detail, "Provide a balanced analysis with essential details.")

                # ICD instruction (always include for medical analysis)
                icd_instruction = "If ICD diagnosis codes are present in the records, include them in your analysis with appropriate context."

                # Use the EXACT prompt from CaseBuilder DetailedMedicalAnalysisHandler
                if style == "narrative":
                    prompt = f"""
                    As an AI medical analyst, your task is to provide an analysis of the medical records for {client_name}.

                    {style_instructions}

                    {detail_instructions}

                    Your narrative should cover the injuries sustained, how they affected the client's life, the timeline of medical care (organized by provider if multiple but preserving the sequence of events), and integrate any user-provided notes to fill in gaps or clarify events.

                    FORMATTING REQUIREMENTS:
                    - Use minimal markdown formatting (avoid **, ##, ###, etc.)
                    - Keep formatting clean and copy-paste friendly
                    - Avoid excessive headers, subheaders, or nested formatting
                    - Use simple text with minimal bold formatting only when absolutely necessary

                    {icd_instruction}

                    Additional Notes (if applicable):
                    {additional_notes}
                    """
                else:
                    prompt = f"""
                    As an AI medical analyst, your task is to provide an analysis of the medical records for {client_name}.

                    {style_instructions}

                    {detail_instructions}

                    Your analysis should include:
                    1. A review of the injuries sustained and how they affected the client's life.
                    2. A timeline of medical care, organized by provider (if multiple) but preserving the sequence of events.
                    3. Integration of user-provided notes (if available) to fill in gaps or clarify events.

                    FORMATTING REQUIREMENTS:
                    - Use minimal markdown formatting (avoid **, ##, ###, etc.)
                    - Keep formatting clean and copy-paste friendly
                    - Avoid excessive headers, subheaders, or nested formatting
                    - Use simple text with minimal bold formatting only when absolutely necessary

                    {icd_instruction}

                    Additional Notes (if applicable):
                    {additional_notes}
                    """

                # Call OpenAI with exact same structure as CaseBuilder
                response = await client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[
                        {"role": "system", "content": "You are an AI medical analyst specializing in personal injury cases."},
                        {"role": "user", "content": f"{prompt}\n\nMedical Records Content:\n{document_text}"}
                    ],
                    max_tokens=4000,
                    temperature=0.3
                )

                result = response.choices[0].message.content
                logger.info(f"OpenAI medical analysis completed: {len(result)} characters")

            except Exception as e:
                logger.error(f"OpenAI error: {str(e)}")
                result = f"Error generating analysis: {str(e)}"

        elif request_data.analysis_type == 'medical_expenses':
            logger.info("Processing medical expenses analysis using exact CaseBuilder prompt")

            try:
                # Get preferences
                prefs = request_data.preferences or {}
                client_name = prefs.get('client_name', 'the client')
                additional_notes = prefs.get('additional_notes', '')

                # Get analysis configuration from preferences
                analysis_config = prefs.get('analysis', {})
                detail_level = analysis_config.get('detail_level', 'standard').lower()
                analysis_style = analysis_config.get('analysis_style', 'tabular').lower()

                # Map frontend values to backend values
                style_mapping = {
                    'narrative': 'narrative',
                    'chronological': 'chronological',
                    'bulleted': 'bulleted',
                    'tabular': 'tabular'
                }
                style = style_mapping.get(analysis_style, 'tabular')

                detail_mapping = {
                    'basic': 'brief',
                    'standard': 'moderate',
                    'comprehensive': 'comprehensive'
                }
                detail = detail_mapping.get(detail_level, 'moderate')

                # Create style instructions
                style_instructions = {
                    'narrative': 'Provide the medical expenses analysis in a detailed narrative format, integrating costs and provider information into the text. IMPORTANT: Do NOT use bullet points, numbered lists, or markdown formatting. Write in complete sentences and paragraphs as if writing a story or essay. Avoid using headers, section titles, or any structured formatting - integrate all information into a continuous narrative that flows naturally.',
                    'chronological': 'Organize the medical expenses analysis in chronological order, listing each expense event by date with associated details.',
                    'bulleted': 'Present the medical expenses analysis as bulleted points for clarity, grouping by provider or category as needed.',
                    'tabular': 'Present the medical expenses in a clear table format.'
                }.get(style, 'Present the medical expenses in a clear table format.')

                # Create detail level instructions
                detail_instructions = {
                    "brief": "Provide a concise summary of only the most significant medical expenses. Group smaller expenses together and focus on major costs.",
                    "moderate": "Provide a balanced analysis of medical expenses with moderate detail. Include all significant costs while summarizing minor expenses.",
                    "comprehensive": "Provide an exhaustive breakdown of all medical expenses with complete detail. Include every charge, no matter how small."
                }.get(detail, "Provide a balanced analysis of medical expenses with moderate detail.")

                # ICD instruction
                icd_instruction = "If ICD diagnosis codes are present in the records, include them in your analysis with appropriate context."

                # Use the EXACT prompt from CaseBuilder MedicalExpensesAnalysisHandler
                if style == "narrative":
                    prompt = f"""
                    As an AI legal expert, your task is to extract and compile medical expenses for {client_name} from the provided documents.
                    {style_instructions}

                    {detail_instructions}

                    In your narrative, be sure to include information about each medical provider/facility, their treatment/procedure summary, treatment period, total cost, and page references where appropriate. Weave this information naturally into your narrative without using lists or bullet points.

                    {icd_instruction}

                    Additional Notes (if applicable):
                    {additional_notes}
                    """
                else:
                    prompt = f"""
                    As an AI legal expert, your task is to extract and compile medical expenses for {client_name} from the provided documents.
                    {style_instructions}

                    {detail_instructions}

                    Specifically, identify and for each medical provider/facility:
                    - Provider/Facility Name
                    - Treatment/Procedure Summary (summarized description)
                    - Treatment Period (From – To)
                    - Total Cost
                    - Include page references [Page X] where appropriate

                    {icd_instruction}

                    Additional Notes (if applicable):
                    {additional_notes}
                    """

                # Call OpenAI with exact same structure as CaseBuilder
                response = await client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[
                        {"role": "system", "content": "You are an AI legal expert specializing in medical expense analysis."},
                        {"role": "user", "content": f"{prompt}\n\nMedical Records Content:\n{document_text}"}
                    ],
                    max_tokens=4000,
                    temperature=0.3
                )

                result = response.choices[0].message.content
                logger.info(f"OpenAI medical expenses analysis completed: {len(result)} characters")

            except Exception as e:
                logger.error(f"OpenAI error: {str(e)}")
                result = f"Error generating analysis: {str(e)}"

        elif request_data.analysis_type == 'future_treatment':
            logger.info("Processing future treatment analysis using exact CaseBuilder prompt")

            try:
                # Get preferences
                prefs = request_data.preferences or {}
                client_name = prefs.get('client_name', 'the client')
                additional_notes = prefs.get('additional_notes', '')

                # Get analysis configuration from preferences
                analysis_config = prefs.get('analysis', {})
                detail_level = analysis_config.get('detail_level', 'standard').lower()
                analysis_style = analysis_config.get('analysis_style', 'tabular').lower()

                # Map frontend values to backend values
                style_mapping = {
                    'narrative': 'narrative',
                    'chronological': 'chronological',
                    'bulleted': 'bulleted',
                    'tabular': 'tabular'
                }
                style = style_mapping.get(analysis_style, 'tabular')

                detail_mapping = {
                    'basic': 'brief',
                    'standard': 'moderate',
                    'comprehensive': 'comprehensive'
                }
                detail = detail_mapping.get(detail_level, 'moderate')

                # Create style instructions
                style_instructions = {
                    'narrative': 'Provide a detailed narrative of the anticipated future medical expenses, explaining their purpose and importance. IMPORTANT: Do NOT use bullet points, numbered lists, or markdown formatting. Write in complete sentences and paragraphs as if writing a story or essay. Avoid using headers, section titles, or any structured formatting - integrate all information into a continuous narrative that flows naturally.',
                    'chronological': 'Organize the future medical expenses chronologically by anticipated date, detailing each expense event.',
                    'bulleted': 'Present the future medical expenses as bulleted items for clarity, grouping by type or provider.',
                    'tabular': 'Present the future medical expenses in a well-structured table format.'
                }.get(style, 'Present the future medical expenses in a well-structured table format.')

                # Create detail level instructions
                detail_instructions = {
                    "brief": "Focus only on the most significant future medical needs. Provide a concise projection of major anticipated expenses.",
                    "moderate": "Provide a balanced projection of future medical needs with reasonable detail. Include all significant anticipated treatments.",
                    "comprehensive": "Provide an exhaustive projection of all possible future medical needs. Include every potential treatment, no matter how minor."
                }.get(detail, "Provide a balanced projection of future medical needs with reasonable detail.")

                # ICD instruction
                icd_instruction = "If ICD diagnosis codes are present in the records, include them in your analysis with appropriate context."

                # Use the EXACT prompt from CaseBuilder FutureMedicalExpensesHandler
                if style == "narrative":
                    prompt = f"""
                    As an AI legal expert, your task is to project foreseeable future medical expenses for {client_name}.
                    {style_instructions}

                    {detail_instructions}

                    In your narrative, be sure to include information about each future medical expense, including frequency and duration, cost per session, and total estimated cost. Weave this information naturally into your narrative without using lists or bullet points.

                    {icd_instruction}

                    Additional Notes (if applicable):
                    {additional_notes}
                    """
                else:
                    prompt = f"""
                    As an AI legal expert, your task is to project foreseeable future medical expenses for {client_name}.
                    {style_instructions}

                    {detail_instructions}

                    Your analysis should identify:
                    - Future Medical Expense
                    - Frequency and Duration
                    - Cost per Session
                    - Total Estimated Cost
                    - For non-tabular formats, integrate these details into the text as appropriate.

                    {icd_instruction}

                    Additional Notes (if applicable):
                    {additional_notes}
                    """

                # Call OpenAI with exact same structure as CaseBuilder
                response = await client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[
                        {"role": "system", "content": "You are an AI legal expert specializing in future medical expense projection."},
                        {"role": "user", "content": f"{prompt}\n\nMedical Records Content:\n{document_text}"}
                    ],
                    max_tokens=4000,
                    temperature=0.3
                )

                result = response.choices[0].message.content
                logger.info(f"OpenAI future treatment analysis completed: {len(result)} characters")

            except Exception as e:
                logger.error(f"OpenAI error: {str(e)}")
                result = f"Error generating analysis: {str(e)}"

        else:
            result = f"Analysis type {request_data.analysis_type} not yet implemented"

        logger.info(f"Direct analysis completed successfully")

        return {
            "result": result,
            "status": "completed",
            "message": "Analysis completed successfully"
        }

    except Exception as e:
        logger.error(f"Error in direct analysis: {str(e)}")
        return {"error": f"Analysis failed: {str(e)}"}


async def process_analysis_background(
    analysis_id: str, 
    session_id: str, 
    analysis_request: AnalysisRequest
):
    """
    Background task to process analysis request.
    
    Args:
        analysis_id: Analysis identifier
        session_id: Session identifier
        analysis_request: Analysis configuration
    """
    try:
        # Get session data
        session_data = session_store.get_session(session_id)
        if not session_data:
            logger.error(f"Session {session_id} not found for analysis processing")
            return
        
        analyses = session_data.get("analyses", {})
        if analysis_id not in analyses:
            logger.error(f"Analysis {analysis_id} not found in session")
            return
        
        # Update status to processing
        analyses[analysis_id]["status"] = "processing"
        analyses[analysis_id]["progress"] = 10
        session_store.update_session(session_id, {"analyses": analyses})
        
        # Integrate with existing CaseBuilder analysis logic
        from ..services.analysis_service import AnalysisService
        analysis_service = AnalysisService()

        # Process analysis with real CaseBuilder integration
        total_analyses = len(analysis_request.selected_analyses)
        results = {}

        for i, analysis_type in enumerate(analysis_request.selected_analyses):
            # Update progress
            progress = 10 + (80 * (i + 1) // total_analyses)
            analyses[analysis_id]["progress"] = progress
            session_store.update_session(session_id, {"analyses": analyses})

            # Process real analysis
            try:
                result = await analysis_service.process_analysis(
                    analysis_type=analysis_type,
                    document_ids=analysis_request.document_ids,
                    preferences=analysis_request.preferences,
                    session_id=session_id
                )
                results[analysis_type.value] = result
            except Exception as e:
                logger.error(f"Error processing {analysis_type.value}: {str(e)}")
                results[analysis_type.value] = f"Error processing {analysis_type.value}: {str(e)}"
        
        # Update status to completed
        analyses[analysis_id]["status"] = "completed"
        analyses[analysis_id]["progress"] = 100
        analyses[analysis_id]["completed_at"] = time.time()
        analyses[analysis_id]["results"] = results
        
        # Update session stats
        stats = session_data.get("stats", {})
        stats["analyses_completed"] = stats.get("analyses_completed", 0) + 1
        
        session_store.update_session(session_id, {
            "analyses": analyses,
            "stats": stats
        })
        
        logger.info(f"Analysis {analysis_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Error processing analysis {analysis_id}: {str(e)}")
        
        # Update status to error
        session_data = session_store.get_session(session_id)
        if session_data:
            analyses = session_data.get("analyses", {})
            if analysis_id in analyses:
                analyses[analysis_id]["status"] = "error"
                analyses[analysis_id]["error"] = str(e)
                session_store.update_session(session_id, {"analyses": analyses})
