"""
Analysis router for CaseBuilder AI.
Handles document analysis requests and results.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
import logging
import uuid
import time
from typing import List, Dict, Any
import asyncio
import aiohttp
import base64
import io
import os

from ..models.requests import AnalysisRequest
from ..models.responses import (
    AnalysisResponse,
    AnalysisListResponse,
    BaseResponse,
    ProgressResponse
)
from ..models.preferences import AnalysisType
from pydantic import BaseModel
from typing import List
from ..services.auth_service import User
from ..dependencies import get_current_user, get_current_session, validate_session_access
from ..middleware.session import session_store

logger = logging.getLogger(__name__)
router = APIRouter()


# Direct analysis request model
class DirectAnalysisFile(BaseModel):
    name: str
    content: str  # base64 content
    type: str


class DirectAnalysisRequest(BaseModel):
    files: List[DirectAnalysisFile]
    preferences: dict
    analysis_type: str


@router.post("/start", response_model=AnalysisResponse)
async def start_analysis(
    analysis_request: AnalysisRequest,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Start document analysis with specified preferences.
    
    Args:
        analysis_request: Analysis configuration
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        AnalysisResponse with analysis details
    """
    try:
        session_id = getattr(request.state, "session_id")
        session_data = await get_current_session(request)

        # Validate that documents exist
        documents = session_data.get("documents", {})
        for doc_id in analysis_request.document_ids:
            if doc_id not in documents:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Document {doc_id} not found"
                )
        
        # Generate analysis ID
        analysis_id = str(uuid.uuid4())
        current_time = time.time()
        
        # Create analysis record
        analysis_data = {
            "id": analysis_id,
            "document_ids": analysis_request.document_ids,
            "preferences": analysis_request.preferences.model_dump(),
            "selected_analyses": [a.value for a in analysis_request.selected_analyses],
            "status": "started",
            "progress": 0,
            "results": {},
            "started_at": current_time,
            "completed_at": None,
            "error": None
        }
        
        # Store analysis in session
        analyses = session_data.get("analyses", {})
        analyses[analysis_id] = analysis_data
        
        session_store.update_session(session_id, {
            "analyses": analyses
        })
        
        # Start background analysis
        asyncio.create_task(
            process_analysis_background(analysis_id, session_id, analysis_request)
        )
        
        logger.info(f"Analysis {analysis_id} started by user {user.username}")

        return AnalysisResponse(
            analysis_id=analysis_id,
            analysis_type="multi_analysis",
            status="started",
            progress=0,
            started_at=current_time,
            message="Analysis started successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting analysis for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start analysis"
        )


@router.get("/", response_model=AnalysisListResponse)
async def list_analyses(
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    List all analyses in the current session.
    
    Args:
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        AnalysisListResponse with list of analyses
    """
    try:
        session_data = await get_current_session(request)
        analyses = session_data.get("analyses", {})
        
        # Convert to response format
        analysis_list = []
        for analysis_id, analysis_data in analyses.items():
            analysis_list.append(AnalysisResponse(
                analysis_id=analysis_id,
                analysis_type="multi_analysis",
                status=analysis_data["status"],
                progress=analysis_data["progress"],
                started_at=analysis_data["started_at"],
                completed_at=analysis_data.get("completed_at")
            ))
        
        return AnalysisListResponse(
            analyses=analysis_list,
            total_count=len(analysis_list),
            message="Analyses retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error listing analyses for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve analyses"
        )


@router.get("/{analysis_id}", response_model=AnalysisResponse)
async def get_analysis(
    analysis_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Get analysis details and results by ID.
    
    Args:
        analysis_id: Analysis identifier
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        AnalysisResponse with analysis details and results
    """
    try:
        session_data = await get_current_session(request)
        analyses = session_data.get("analyses", {})
        
        if analysis_id not in analyses:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )
        
        analysis_data = analyses[analysis_id]
        
        # Get results if completed
        result = None
        if analysis_data["status"] == "completed" and analysis_data["results"]:
            # Combine all results into a single string for now
            # In a real implementation, this would be more structured
            result_parts = []
            for analysis_type, analysis_result in analysis_data["results"].items():
                result_parts.append(f"=== {analysis_type.upper()} ===\n{analysis_result}\n")
            result = "\n".join(result_parts)
        
        return AnalysisResponse(
            analysis_id=analysis_id,
            analysis_type="multi_analysis",
            status=analysis_data["status"],
            result=result,
            progress=analysis_data["progress"],
            started_at=analysis_data["started_at"],
            completed_at=analysis_data.get("completed_at"),
            message="Analysis retrieved successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting analysis {analysis_id} for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve analysis"
        )


@router.get("/{analysis_id}/progress", response_model=ProgressResponse)
async def get_analysis_progress(
    analysis_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Get analysis progress.
    
    Args:
        analysis_id: Analysis identifier
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        ProgressResponse with analysis progress
    """
    try:
        session_data = await get_current_session(request)
        analyses = session_data.get("analyses", {})
        
        if analysis_id not in analyses:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )
        
        analysis_data = analyses[analysis_id]
        
        return ProgressResponse(
            task_id=analysis_id,
            status=analysis_data["status"],
            progress=analysis_data["progress"],
            message=f"Analysis {analysis_data['status']}",
            started_at=analysis_data["started_at"],
            updated_at=time.time()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting progress for analysis {analysis_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get analysis progress"
        )


@router.delete("/{analysis_id}", response_model=BaseResponse)
async def cancel_analysis(
    analysis_id: str,
    request: Request,
    user: User = Depends(get_current_user),
    _: bool = Depends(validate_session_access)
):
    """
    Cancel or delete an analysis.
    
    Args:
        analysis_id: Analysis identifier
        request: FastAPI request object
        user: Current authenticated user
        
    Returns:
        BaseResponse confirming cancellation
    """
    try:
        session_id = getattr(request.state, "session_id")
        session_data = await get_current_session(request)
        analyses = session_data.get("analyses", {})
        
        if analysis_id not in analyses:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Analysis not found"
            )
        
        # Remove analysis from session
        del analyses[analysis_id]
        
        session_store.update_session(session_id, {
            "analyses": analyses
        })
        
        logger.info(f"Analysis {analysis_id} cancelled by user {user.username}")
        
        return BaseResponse(message="Analysis cancelled successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling analysis {analysis_id} for user {user.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel analysis"
        )


@router.post("/direct")
async def direct_analysis(
    request_data: DirectAnalysisRequest
    # Temporarily remove authentication for testing
    # user: User = Depends(get_current_user)
):
    """
    Direct analysis endpoint that processes files from browser memory.
    Uses the existing CaseBuilder StreamlitAdapter.
    """
    try:
        logger.info(f"Starting direct analysis with {len(request_data.files)} files")

        # Use OpenAI directly with the exact same prompt as CaseBuilder
        from openai import AsyncOpenAI
        import os

        client = AsyncOpenAI(api_key=os.getenv('OPENAI_API_KEY'))

        logger.info("OpenAI client setup successful")

        # Extract text from files (simplified for testing)
        combined_text = []
        for file_data in request_data.files:
            try:
                logger.info(f"Processing file: {file_data.name}, type: {file_data.type}")

                # Decode base64 content
                import base64
                try:
                    # Handle data URLs (data:type;base64,content)
                    if ',' in file_data.content:
                        file_content = base64.b64decode(file_data.content.split(',')[1])
                    else:
                        file_content = base64.b64decode(file_data.content)

                    logger.info(f"Decoded {len(file_content)} bytes from {file_data.name}")
                except Exception as decode_error:
                    logger.error(f"Base64 decode error for {file_data.name}: {str(decode_error)}")
                    continue

                # Extract text based on file type
                text = ""
                if file_data.type == "application/pdf":
                    # PDF text extraction with detailed logging
                    try:
                        from PyPDF2 import PdfReader
                        from io import BytesIO
                        pdf_file = BytesIO(file_content)
                        pdf_reader = PdfReader(pdf_file)
                        logger.info(f"PDF {file_data.name} has {len(pdf_reader.pages)} pages")

                        text_parts = []
                        for i, page in enumerate(pdf_reader.pages):
                            try:
                                page_text = page.extract_text()
                                logger.info(f"Page {i+1}: extracted {len(page_text)} characters")
                                if page_text.strip():
                                    text_parts.append(page_text)
                                    logger.info(f"Page {i+1}: added {len(page_text.strip())} non-empty characters")
                                else:
                                    logger.warning(f"Page {i+1}: no text content found")
                            except Exception as page_error:
                                logger.error(f"Error extracting text from page {i+1}: {str(page_error)}")

                        text = "\n".join(text_parts)
                        logger.info(f"Total extracted {len(text)} characters from PDF {file_data.name}")

                        # If no text extracted, try OCR with GPT-4o Vision
                        if not text.strip():
                            logger.warning(f"No text extracted from PDF {file_data.name}, trying OCR with GPT-4o Vision")
                            try:
                                # Convert PDF to images and use OCR
                                from pdf2image import convert_from_bytes
                                import base64

                                # Convert PDF to images with optimized settings
                                images = convert_from_bytes(
                                    file_content,
                                    dpi=150,  # Higher DPI for better OCR accuracy
                                    thread_count=2,  # Reduce threads to avoid memory issues
                                    use_cropbox=True,
                                    grayscale=True
                                )

                                # Limit pages for faster processing (first 5 pages for testing)
                                max_pages = min(5, len(images))
                                images = images[:max_pages]
                                logger.info(f"Processing {len(images)} pages (limited to {max_pages}) for OCR")

                                # Process pages in parallel with limited concurrency
                                async def process_page_ocr(i, image):
                                    try:
                                        # Convert PIL image to bytes
                                        img_byte_arr = io.BytesIO()
                                        image.save(img_byte_arr, format='PNG', optimize=True)
                                        img_bytes = img_byte_arr.getvalue()

                                        # Encode to base64
                                        base64_image = base64.b64encode(img_bytes).decode('utf-8')

                                        # Call GPT-4o Vision for OCR
                                        ocr_payload = {
                                            "model": "gpt-4o-2024-05-13",
                                            "messages": [
                                                {
                                                    "role": "user",
                                                    "content": [
                                                        {
                                                            "type": "text",
                                                            "text": "Extract all text from this document page. Maintain structure and formatting. Return only the extracted text."
                                                        },
                                                        {
                                                            "type": "image_url",
                                                            "image_url": {
                                                                "url": f"data:image/png;base64,{base64_image}"
                                                            }
                                                        }
                                                    ]
                                                }
                                            ],
                                            "max_tokens": 3000  # Reduced for faster processing
                                        }

                                        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
                                            async with session.post(
                                                "https://api.openai.com/v1/chat/completions",
                                                headers={
                                                    "Content-Type": "application/json",
                                                    "Authorization": f"Bearer {os.getenv('OPENAI_API_KEY')}"
                                                },
                                                json=ocr_payload
                                            ) as response:
                                                if response.status == 200:
                                                    response_json = await response.json()
                                                    choices = response_json.get('choices', [])
                                                    if choices:
                                                        page_text = choices[0]['message']['content']
                                                        if page_text.strip():
                                                            logger.info(f"OCR extracted {len(page_text)} characters from page {i+1}")
                                                            return f"=== Page {i+1} ===\n{page_text}"
                                                        else:
                                                            logger.warning(f"OCR found no text on page {i+1}")
                                                            return None
                                                    else:
                                                        logger.warning(f"No OCR response for page {i+1}")
                                                        return None
                                                else:
                                                    logger.error(f"OCR API error for page {i+1}: {response.status}")
                                                    return None
                                    except Exception as page_ocr_error:
                                        logger.error(f"OCR error for page {i+1}: {str(page_ocr_error)}")
                                        return None

                                # Process pages with limited concurrency (2 at a time)
                                semaphore = asyncio.Semaphore(2)

                                async def process_with_semaphore(i, image):
                                    async with semaphore:
                                        return await process_page_ocr(i, image)

                                # Create tasks for all pages
                                tasks = [process_with_semaphore(i, image) for i, image in enumerate(images)]

                                # Wait for all tasks to complete
                                ocr_results = await asyncio.gather(*tasks, return_exceptions=True)

                                # Filter successful results
                                ocr_text_parts = [result for result in ocr_results if isinstance(result, str) and result]

                                if ocr_text_parts:
                                    text = "\n\n".join(ocr_text_parts)
                                    logger.info(f"OCR extracted total {len(text)} characters from PDF {file_data.name}")
                                else:
                                    text = f"[PDF {file_data.name} - both text extraction and OCR failed]"
                                    logger.error(f"Both text extraction and OCR failed for {file_data.name}")

                            except Exception as ocr_error:
                                logger.error(f"OCR processing error for {file_data.name}: {str(ocr_error)}")
                                text = f"[PDF {file_data.name} - text extraction failed, OCR error: {str(ocr_error)}]"

                    except Exception as pdf_error:
                        logger.error(f"PDF extraction error for {file_data.name}: {str(pdf_error)}")
                        text = f"Error extracting PDF text: {str(pdf_error)}"

                elif file_data.type.startswith("text/") or file_data.name.endswith('.txt'):
                    # Text file
                    try:
                        text = file_content.decode('utf-8')
                        logger.info(f"Extracted {len(text)} characters from text file {file_data.name}")
                    except UnicodeDecodeError:
                        try:
                            text = file_content.decode('latin-1')
                            logger.info(f"Extracted {len(text)} characters from text file {file_data.name} (latin-1)")
                        except Exception as text_error:
                            logger.error(f"Text decode error for {file_data.name}: {str(text_error)}")
                            text = f"Could not decode text from {file_data.name}"
                else:
                    # Unknown file type - try as text
                    try:
                        text = file_content.decode('utf-8')
                        logger.info(f"Extracted {len(text)} characters from unknown type {file_data.name}")
                    except:
                        text = f"Unsupported file type: {file_data.type}"

                if text.strip():
                    combined_text.append(f"=== {file_data.name} ===\n{text}\n")
                    logger.info(f"Added text from {file_data.name} to combined text")
                else:
                    logger.warning(f"No text content found in {file_data.name}")

            except Exception as e:
                logger.error(f"Error processing file {file_data.name}: {str(e)}")
                combined_text.append(f"=== {file_data.name} ===\nError processing file: {str(e)}\n")

        if not combined_text:
            logger.warning("No text content extracted from files")
            return {"error": "No text content could be extracted from the uploaded files"}

        document_text = "\n".join(combined_text)
        logger.info(f"Extracted text length: {len(document_text)} characters")

        # Process analysis using exact CaseBuilder prompt
        if request_data.analysis_type == 'police_report_summary':
            logger.info("Processing police report summary using exact CaseBuilder prompt")

            try:
                # Get preferences
                prefs = request_data.preferences or {}
                client_name = prefs.get('client_name', 'the client')
                additional_notes = prefs.get('additional_notes', '')

                # Use the EXACT prompt from CaseBuilder FactsOfLossHandler
                prompt = f"""
                You are an AI legal expert specializing in personal injury cases.
                Your task is to produce a clear, concise summary of the police report for a traffic collision.

                Provide a balanced analysis with essential details about the accident and liability determination.

                Extract and summarize the following key information:
                1. Date, time, and location of the accident
                2. Parties involved (vehicles, drivers, passengers)
                3. Brief description of how the accident occurred
                4. Weather and road conditions
                5. Reported injuries (if any)
                6. Citations issued (if any)
                7. Officer's determination of fault/liability (if stated in the report)

                For the liability determination (#7), include the officer's opinion if it's stated in the report, but clearly label it as "Officer's Opinion" and add this note: "Note: The officer's opinion on fault is not a legal determination and may not reflect the actual legal liability in this case."
                This is just a factual summary of what's in the report, including the officer's stated conclusions.

                Format your response in a structured, easy-to-read format with clear headings.
                Keep your summary brief and focused on the most important details.

                **Additional Notes (if applicable):**
                {additional_notes}
                """

                # Call OpenAI with exact same structure as CaseBuilder
                response = await client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": "You are an AI legal expert specializing in personal injury cases."},
                        {"role": "user", "content": f"{prompt}\n\nPolice Report Content:\n{document_text}"}
                    ],
                    max_tokens=4000,
                    temperature=0.3
                )

                result = response.choices[0].message.content
                logger.info(f"OpenAI analysis completed: {len(result)} characters")

            except Exception as e:
                logger.error(f"OpenAI error: {str(e)}")
                result = f"Error generating analysis: {str(e)}"

        else:
            result = f"Analysis type {request_data.analysis_type} not yet implemented"

        logger.info(f"Direct analysis completed successfully")

        return {
            "result": result,
            "status": "completed",
            "message": "Analysis completed successfully"
        }

    except Exception as e:
        logger.error(f"Error in direct analysis: {str(e)}")
        return {"error": f"Analysis failed: {str(e)}"}


async def process_analysis_background(
    analysis_id: str, 
    session_id: str, 
    analysis_request: AnalysisRequest
):
    """
    Background task to process analysis request.
    
    Args:
        analysis_id: Analysis identifier
        session_id: Session identifier
        analysis_request: Analysis configuration
    """
    try:
        # Get session data
        session_data = session_store.get_session(session_id)
        if not session_data:
            logger.error(f"Session {session_id} not found for analysis processing")
            return
        
        analyses = session_data.get("analyses", {})
        if analysis_id not in analyses:
            logger.error(f"Analysis {analysis_id} not found in session")
            return
        
        # Update status to processing
        analyses[analysis_id]["status"] = "processing"
        analyses[analysis_id]["progress"] = 10
        session_store.update_session(session_id, {"analyses": analyses})
        
        # Integrate with existing CaseBuilder analysis logic
        from ..services.analysis_service import AnalysisService
        analysis_service = AnalysisService()

        # Process analysis with real CaseBuilder integration
        total_analyses = len(analysis_request.selected_analyses)
        results = {}

        for i, analysis_type in enumerate(analysis_request.selected_analyses):
            # Update progress
            progress = 10 + (80 * (i + 1) // total_analyses)
            analyses[analysis_id]["progress"] = progress
            session_store.update_session(session_id, {"analyses": analyses})

            # Process real analysis
            try:
                result = await analysis_service.process_analysis(
                    analysis_type=analysis_type,
                    document_ids=analysis_request.document_ids,
                    preferences=analysis_request.preferences,
                    session_id=session_id
                )
                results[analysis_type.value] = result
            except Exception as e:
                logger.error(f"Error processing {analysis_type.value}: {str(e)}")
                results[analysis_type.value] = f"Error processing {analysis_type.value}: {str(e)}"
        
        # Update status to completed
        analyses[analysis_id]["status"] = "completed"
        analyses[analysis_id]["progress"] = 100
        analyses[analysis_id]["completed_at"] = time.time()
        analyses[analysis_id]["results"] = results
        
        # Update session stats
        stats = session_data.get("stats", {})
        stats["analyses_completed"] = stats.get("analyses_completed", 0) + 1
        
        session_store.update_session(session_id, {
            "analyses": analyses,
            "stats": stats
        })
        
        logger.info(f"Analysis {analysis_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Error processing analysis {analysis_id}: {str(e)}")
        
        # Update status to error
        session_data = session_store.get_session(session_id)
        if session_data:
            analyses = session_data.get("analyses", {})
            if analysis_id in analyses:
                analyses[analysis_id]["status"] = "error"
                analyses[analysis_id]["error"] = str(e)
                session_store.update_session(session_id, {"analyses": analyses})
