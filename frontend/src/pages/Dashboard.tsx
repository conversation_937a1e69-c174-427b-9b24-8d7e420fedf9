/**
 * Main Dashboard page
 */

import React, { useEffect, useState } from 'react';
import {
  Settings,
  Upload,
  Play,
  Clock,
  CheckCircle,
  AlertCircle,
  Trash2
} from 'lucide-react';
import { usePreferencesStore } from '../store/preferencesStore';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { PreferencesPanel } from '../components/dashboard/PreferencesPanel';
import { StatsCards } from '../components/dashboard/StatsCards';
import { FileUpload } from '../components/dashboard/FileUpload';
import { ProcessingModal } from '../components/ui/ProcessingModal';
import toast from 'react-hot-toast';

export const Dashboard: React.FC = () => {
  const { loadPreferences, loadAnalysisTypes, preferences } = usePreferencesStore();
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [documentIds, setDocumentIds] = useState<string[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<string | null>(null);
  const [showResult, setShowResult] = useState(false);

  // Processing modal state
  const [showProcessingModal, setShowProcessingModal] = useState(false);
  const [processingStep, setProcessingStep] = useState<string>('starting');
  const [processingProgress, setProcessingProgress] = useState(0);
  const [completedAnalyses, setCompletedAnalyses] = useState<string[]>([]);
  const [processingError, setProcessingError] = useState<string | null>(null);

  useEffect(() => {
    // Load initial data
    loadPreferences();
    loadAnalysisTypes();

    // Check for existing files in sessionStorage on component mount
    checkStoredFiles();
  }, [loadPreferences, loadAnalysisTypes]);

  // Listen for storage changes to update file count
  useEffect(() => {
    const handleStorageChange = () => {
      checkStoredFiles();
    };

    // Listen for custom events when files are added/removed
    window.addEventListener('casebuilder-files-changed', handleStorageChange);

    // Also check periodically in case we miss events
    const interval = setInterval(checkStoredFiles, 1000);

    return () => {
      window.removeEventListener('casebuilder-files-changed', handleStorageChange);
      clearInterval(interval);
    };
  }, []);

  const checkStoredFiles = () => {
    const storedFiles = JSON.parse(sessionStorage.getItem('casebuilder-files') || '[]');
    if (storedFiles.length > 0) {
      // Update state to reflect stored files
      setDocumentIds(storedFiles.map((f: any) => f.id));
      // Create mock File objects for display purposes
      const mockFiles = storedFiles.map((f: any) => ({
        name: f.name,
        size: f.size,
        type: f.type,
        lastModified: f.lastModified
      }));
      setUploadedFiles(mockFiles);
    } else {
      // Clear state if no files
      setDocumentIds([]);
      setUploadedFiles([]);
    }
  };

  const handleFilesUploaded = (files: File[], docIds: string[]) => {
    setUploadedFiles(prev => [...prev, ...files]);
    setDocumentIds(prev => [...prev, ...docIds]);
  };

  const handleBackToDashboard = () => {
    setShowResult(false);
    setAnalysisResult(null);
  };

  const handleClearFiles = () => {
    // Clear files from sessionStorage
    sessionStorage.removeItem('casebuilder-files');
    sessionStorage.removeItem('casebuilder-analysis-results');

    // Clear state
    setUploadedFiles([]);
    setDocumentIds([]);

    toast.success('All files cleared successfully');
  };

  const handleStartAnalysis = async () => {
    if (documentIds.length === 0) {
      toast.error('Please upload documents before starting analysis');
      return;
    }

    if (preferences.analysis.selected_analyses.length === 0) {
      toast.error('Please select at least one analysis type in preferences');
      return;
    }

    // Reset processing state
    setProcessingStep('starting');
    setProcessingProgress(0);
    setCompletedAnalyses([]);
    setProcessingError(null);
    setShowProcessingModal(true);
    setIsAnalyzing(true);

    try {
      // Get current preferences
      const { preferences } = usePreferencesStore.getState();

      // Show progress toast
      const progressToast = toast.loading('Starting Police Report Summary analysis...');

      // Start real analysis process
      await startRealAnalysis(documentIds, preferences, progressToast);

    } catch (error) {
      console.error('Analysis failed:', error);
      setProcessingError(error instanceof Error ? error.message : 'Unknown error');
      setProcessingStep('error');
      toast.error('Analysis failed. Please try again.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const startRealAnalysis = async (documentIds: string[], preferences: any, progressToast: string) => {
    try {
      // Get files from sessionStorage
      const storedFiles = JSON.parse(sessionStorage.getItem('casebuilder-files') || '[]');

      if (storedFiles.length === 0) {
        throw new Error('No files found in memory');
      }

      // Step 1: Send files directly to analysis endpoint
      setProcessingStep('extracting');
      setProcessingProgress(20);

      // Create a direct analysis request with file content
      const analysisRequest = {
        files: storedFiles.map((file: any) => ({
          name: file.name,
          content: file.content, // base64 content
          type: file.type
        })),
        preferences: preferences,
        analysis_type: 'police_report_summary'
      };

      // Step 2: Call analysis endpoint directly
      setProcessingStep('analyzing');
      setProcessingProgress(50);

      const { apiClient } = await import('../services/api');
      const response = await apiClient.post('/analysis/direct', analysisRequest);

      console.log('Analysis response:', response.data);
      console.log('Response result:', response.data.result);
      console.log('Response status:', response.data.status);

      // Step 3: Show result
      setProcessingProgress(100);
      setProcessingStep('completed');

      // Store result in sessionStorage
      const analysisId = `analysis_${Date.now()}`;
      const analysisData = {
        id: analysisId,
        type: 'police_report_summary',
        status: 'completed',
        result: response.data.result,
        files: storedFiles.map((f: any) => ({ id: f.id, name: f.name })),
        preferences: preferences,
        created_at: new Date().toISOString(),
        completed_at: new Date().toISOString()
      };

      // Store analysis result
      const existingAnalyses = JSON.parse(sessionStorage.getItem('casebuilder-analyses') || '[]');
      existingAnalyses.push(analysisData);
      sessionStorage.setItem('casebuilder-analyses', JSON.stringify(existingAnalyses));

      // Show result in the page after modal closes
      setTimeout(() => {
        if (response.data.result && response.data.result.trim()) {
          setAnalysisResult(response.data.result);
          setShowResult(true);
          setShowProcessingModal(false);
        } else {
          throw new Error('No analysis result received from server');
        }
      }, 2000); // Give user time to see completion

    } catch (error) {
      console.error('Analysis error:', error);
      throw error;
    }
  };



  const generateMockPoliceReportSummary = (files: any[], preferences: any) => {
    const currentDate = new Date().toLocaleDateString();
    const fileNames = files.map(f => f.name).join(', ');

    return `# POLICE REPORT SUMMARY

**Generated on:** ${currentDate}
**Documents Analyzed:** ${fileNames}
**Analysis Type:** Police Report Summary
**Detail Level:** ${preferences?.analysis_detail_level || 'Standard'}

## INCIDENT OVERVIEW

**Date of Incident:** [Date extracted from police report]
**Time of Incident:** [Time extracted from police report]
**Location:** [Location details from report]
**Report Number:** [Police report number]
**Reporting Officer:** [Officer name and badge number]

## PARTIES INVOLVED

### Primary Party
- **Name:** [Name from report]
- **Role:** [Driver/Pedestrian/Passenger]
- **Vehicle:** [Vehicle description if applicable]
- **Insurance:** [Insurance information if available]

### Secondary Party
- **Name:** [Name from report]
- **Role:** [Driver/Pedestrian/Passenger]
- **Vehicle:** [Vehicle description if applicable]
- **Insurance:** [Insurance information if available]

## INCIDENT DESCRIPTION

Based on the police report analysis, the incident occurred when [detailed description of the sequence of events leading to the incident]. The officer's investigation revealed [key findings from the report].

**Weather Conditions:** [Weather at time of incident]
**Road Conditions:** [Road surface and visibility conditions]
**Traffic Control:** [Traffic signals, signs, or other controls present]

## WITNESS STATEMENTS

The following witnesses were identified and provided statements:
1. [Witness name and contact information]
2. [Additional witnesses as documented]

## OFFICER'S FINDINGS

The investigating officer determined that [officer's conclusions about fault, violations, or contributing factors]. Citations were issued for [any traffic violations noted].

## EVIDENCE COLLECTED

- [List of physical evidence]
- [Photographs taken]
- [Measurements or diagrams]
- [Other documentation]

## LIABILITY ASSESSMENT

**Primary Contributing Factors:**
- [Factor 1 with explanation]
- [Factor 2 with explanation]
- [Additional factors as identified]

**Potential Liability Distribution:**
- Party 1: [Percentage and reasoning]
- Party 2: [Percentage and reasoning]

## RECOMMENDATIONS

Based on this analysis, the following actions are recommended:
1. [Recommendation 1]
2. [Recommendation 2]
3. [Additional recommendations]

---
*This summary was generated using CaseBuilder AI analysis tools. Please review all source documents for complete accuracy.*`;
  };

  // Debug logs
  console.log('Dashboard render - showResult:', showResult, 'analysisResult length:', analysisResult?.length || 0);

  // Show analysis result if available
  if (showResult && analysisResult) {
    console.log('Rendering analysis result:', { showResult, analysisResult: analysisResult?.substring(0, 100) + '...' });
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Analysis Results</h1>
            <p className="text-lg text-gray-600 mt-2">
              Analysis completed successfully
            </p>
          </div>
          <div className="flex space-x-3">
            <Button
              onClick={handleBackToDashboard}
              variant="secondary"
              className="flex items-center"
            >
              ← Back to Dashboard
            </Button>
            <Button
              onClick={() => {
                // TODO: Implement regenerate functionality
                toast.success('Regenerate functionality coming soon!');
              }}
              className="flex items-center"
            >
              🔄 Regenerate
            </Button>
            <Button
              onClick={() => {
                // Get the analysis type from sessionStorage for filename
                const existingAnalyses = JSON.parse(sessionStorage.getItem('casebuilder-analyses') || '[]');
                const latestAnalysis = existingAnalyses[existingAnalyses.length - 1];
                const analysisType = latestAnalysis?.type || 'analysis';

                const blob = new Blob([analysisResult], { type: 'text/markdown' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${analysisType.replace(/_/g, '-')}-result.md`;
                a.click();
                URL.revokeObjectURL(url);
                toast.success('Document downloaded!');
              }}
              className="flex items-center"
            >
              📥 Download
            </Button>
          </div>
        </div>

        {/* Analysis Result */}
        <Card>
          <Card.Body>
            <div className="bg-white p-6 rounded-lg">
              <div
                className="whitespace-pre-wrap font-mono text-sm leading-relaxed text-gray-800 bg-gray-50 p-4 rounded border"
                style={{ minHeight: '400px' }}
              >
                {analysisResult || 'No analysis result available'}
              </div>
            </div>
          </Card.Body>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Configure your preferences and upload documents for analysis
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <StatsCards />

      {/* Document Upload - Full Width */}
      <Card>
        <Card.Header>
          <div className="flex items-center">
            <Upload className="w-5 h-5 text-primary-400 mr-2" />
            <h2 className="text-lg font-semibold text-gray-900">
              Upload Documents
            </h2>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Upload PDFs, images, and documents for analysis
          </p>
        </Card.Header>
        <Card.Body>
          <FileUpload onFilesUploaded={handleFilesUploaded} />
        </Card.Body>
      </Card>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Preferences Panel - Takes 2 columns on large screens */}
        <div className="lg:col-span-2">
          <Card>
            <Card.Header>
              <div className="flex items-center">
                <Settings className="w-5 h-5 text-primary-400 mr-2" />
                <h2 className="text-lg font-semibold text-gray-900">
                  Analysis Preferences
                </h2>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                Configure your analysis settings and document processing preferences
              </p>
            </Card.Header>
            <Card.Body>
              <PreferencesPanel />
            </Card.Body>
          </Card>
        </div>

        {/* Workflow Panel - Takes 1 column */}
        <div className="space-y-6">

          {/* Generate Button */}
          <Card>
            <Card.Header>
              <div className="flex items-center">
                <Play className="w-5 h-5 text-success-500 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Generate Analysis
                </h3>
              </div>
            </Card.Header>
            <Card.Body>
              <div className="text-center py-4">
                <p className="text-gray-600 mb-4 text-sm">
                  {uploadedFiles.length === 0
                    ? 'Upload documents and configure preferences to enable generation'
                    : preferences.analysis.selected_analyses.length === 0
                    ? `${uploadedFiles.length} document(s) uploaded - Select analysis types to continue`
                    : `${uploadedFiles.length} document(s) ready for analysis`
                  }
                </p>
                <Button
                  variant="success"
                  size="lg"
                  className="w-full"
                  disabled={uploadedFiles.length === 0 || preferences.analysis.selected_analyses.length === 0 || isAnalyzing}
                  onClick={handleStartAnalysis}
                >
                  <Play className="w-4 h-4 mr-2" />
                  {isAnalyzing ? 'Starting Analysis...' : 'Generate Analysis'}
                </Button>
                {(uploadedFiles.length === 0 || preferences.analysis.selected_analyses.length === 0) && (
                  <p className="text-xs text-gray-500 mt-2">
                    {uploadedFiles.length === 0
                      ? 'Upload documents to enable this feature'
                      : 'Select analysis types in preferences to enable generation'
                    }
                  </p>
                )}
              </div>
            </Card.Body>
          </Card>

          {/* Quick Actions */}
          <Card>
            <Card.Header>
              <h3 className="text-lg font-semibold text-gray-900">
                Quick Actions
              </h3>
            </Card.Header>
            <Card.Body>
              <div className="space-y-3">
                <Button variant="secondary" size="sm" className="w-full justify-start">
                  <Clock className="w-4 h-4 mr-2" />
                  View Recent Sessions
                </Button>
                <Button variant="secondary" size="sm" className="w-full justify-start">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Analysis History
                </Button>
                <Button
                  variant="secondary"
                  size="sm"
                  className="w-full justify-start"
                  onClick={handleClearFiles}
                  disabled={uploadedFiles.length === 0}
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Clear All Files
                </Button>
                <Button variant="secondary" size="sm" className="w-full justify-start">
                  <AlertCircle className="w-4 h-4 mr-2" />
                  Help & Support
                </Button>
              </div>
            </Card.Body>
          </Card>
        </div>
      </div>

      {/* Processing Modal */}
      <ProcessingModal
        isOpen={showProcessingModal}
        onClose={() => {
          if (processingStep === 'completed' || processingStep === 'error') {
            setShowProcessingModal(false);
          }
        }}
        currentStep={processingStep}
        progress={processingProgress}
        analysisTypes={['police_report_summary']}
        completedAnalyses={completedAnalyses}
        errorMessage={processingError || undefined}
      />
    </div>
  );
};
