@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }
  
  * {
    @apply border-gray-200;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-400 text-white hover:bg-primary-500 focus:ring-primary-400 shadow-sm hover:shadow-md;
  }
  
  .btn-secondary {
    @apply btn bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-400 border border-gray-300;
  }
  
  .btn-success {
    @apply btn bg-success-500 text-white hover:bg-success-600 focus:ring-success-400;
  }
  
  .btn-warning {
    @apply btn bg-warning-500 text-white hover:bg-warning-600 focus:ring-warning-400;
  }
  
  .btn-error {
    @apply btn bg-error-500 text-white hover:bg-error-600 focus:ring-error-400;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  /* Card Components */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-gray-100 overflow-hidden;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-100 bg-gray-50/50;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-100 bg-gray-50/50;
  }
  
  /* Form Components */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-primary-400 transition-colors duration-200;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }
  
  .form-error {
    @apply text-sm text-error-600 mt-1;
  }
  
  .form-help {
    @apply text-sm text-gray-500 mt-1;
  }
  
  /* Checkbox and Radio */
  .form-checkbox {
    @apply h-4 w-4 text-primary-400 border-gray-300 rounded focus:ring-primary-400 focus:ring-2;
  }
  
  .form-radio {
    @apply h-4 w-4 text-primary-400 border-gray-300 focus:ring-primary-400 focus:ring-2;
  }
  
  /* Progress Components */
  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2 overflow-hidden;
  }
  
  .progress-fill {
    @apply h-full bg-primary-400 transition-all duration-300 ease-out;
  }
  
  /* Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-error {
    @apply badge bg-error-100 text-error-800;
  }
  
  .badge-gray {
    @apply badge bg-gray-100 text-gray-800;
  }
  
  /* Loading Spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-400;
  }
  
  /* Dropdown */
  .dropdown-menu {
    @apply absolute right-0 mt-2 w-56 origin-top-right bg-white border border-gray-200 rounded-lg shadow-medium z-50;
  }
  
  .dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors duration-150;
  }
  
  /* Animations */
  .fade-in {
    @apply animate-fade-in;
  }
  
  .slide-up {
    @apply animate-slide-up;
  }
  
  /* Custom scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f9fafb;
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .bg-gradient-primary {
    background: linear-gradient(135deg, #22d3ee 0%, #06b6d4 100%);
  }
  
  .bg-gradient-success {
    background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
  }
  
  .bg-gradient-warning {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  }
  
  .bg-gradient-error {
    background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
  }
}
