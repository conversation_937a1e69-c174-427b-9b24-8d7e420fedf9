/**
 * Processing Modal Component
 * Shows analysis progress with animated indicators
 */

import React from 'react';
import { X, FileText, Brain, CheckCircle, Clock, AlertCircle } from 'lucide-react';

interface ProcessingModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentStep: string;
  progress: number;
  analysisTypes: string[];
  completedAnalyses: string[];
  errorMessage?: string;
}

export const ProcessingModal: React.FC<ProcessingModalProps> = ({
  isOpen,
  onClose,
  currentStep,
  progress,
  analysisTypes,
  completedAnalyses,
  errorMessage
}) => {
  if (!isOpen) return null;

  const getAnalysisDisplayName = (type: string) => {
    const names: { [key: string]: string } = {
      'police_report_summary': 'Police Report Summary',
      'facts_liability': 'Facts & Liability Analysis',
      'in_depth_liability': 'In-Depth Liability Analysis',
      'medical_analysis': 'Medical Analysis',
      'medical_expenses': 'Medical Expenses',
      'future_treatment': 'Future Treatment',
      'analyze_injuries': 'Injury Analysis',
      'accident_scene': 'Accident Scene Analysis'
    };
    return names[type] || type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getStepIcon = (step: string) => {
    switch (step) {
      case 'starting':
        return <Clock className="w-5 h-5 text-blue-500 animate-spin" />;
      case 'extracting':
        return <FileText className="w-5 h-5 text-yellow-500 animate-pulse" />;
      case 'analyzing':
        return <Brain className="w-5 h-5 text-purple-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStepMessage = (step: string) => {
    switch (step) {
      case 'starting':
        return 'Initializing analysis process...';
      case 'extracting':
        return 'Extracting text from documents...';
      case 'analyzing':
        return 'Performing AI analysis...';
      case 'completed':
        return 'Analysis completed successfully!';
      case 'error':
        return errorMessage || 'An error occurred during analysis';
      default:
        return 'Processing...';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Processing Analysis
          </h3>
          {currentStep === 'completed' || currentStep === 'error' ? (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          ) : null}
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Current Step */}
          <div className="flex items-center mb-6">
            {getStepIcon(currentStep)}
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">
                {getStepMessage(currentStep)}
              </p>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>

          {/* Analysis Types */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-700">Analysis Types:</h4>
            {analysisTypes.map((type, index) => {
              const isCompleted = completedAnalyses.includes(type);
              const isCurrent = !isCompleted && completedAnalyses.length === index;
              
              return (
                <div
                  key={type}
                  className={`flex items-center p-3 rounded-lg border ${
                    isCompleted
                      ? 'bg-green-50 border-green-200'
                      : isCurrent
                      ? 'bg-blue-50 border-blue-200'
                      : 'bg-gray-50 border-gray-200'
                  }`}
                >
                  <div className="flex-shrink-0">
                    {isCompleted ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : isCurrent ? (
                      <Brain className="w-4 h-4 text-blue-500 animate-pulse" />
                    ) : (
                      <Clock className="w-4 h-4 text-gray-400" />
                    )}
                  </div>
                  <span
                    className={`ml-3 text-sm ${
                      isCompleted
                        ? 'text-green-700 font-medium'
                        : isCurrent
                        ? 'text-blue-700 font-medium'
                        : 'text-gray-600'
                    }`}
                  >
                    {getAnalysisDisplayName(type)}
                  </span>
                </div>
              );
            })}
          </div>

          {/* Error Message */}
          {currentStep === 'error' && errorMessage && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-700">{errorMessage}</p>
            </div>
          )}

          {/* Estimated Time */}
          {currentStep !== 'completed' && currentStep !== 'error' && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-xs text-blue-700">
                <Clock className="w-3 h-3 inline mr-1" />
                Estimated time: 2-5 minutes depending on document length
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        {(currentStep === 'completed' || currentStep === 'error') && (
          <div className="px-6 py-4 border-t border-gray-200">
            <button
              onClick={onClose}
              className={`w-full px-4 py-2 rounded-lg font-medium transition-colors ${
                currentStep === 'completed'
                  ? 'bg-green-500 hover:bg-green-600 text-white'
                  : 'bg-red-500 hover:bg-red-600 text-white'
              }`}
            >
              {currentStep === 'completed' ? 'View Results' : 'Close'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
