/**
 * Statistics cards for the dashboard
 */

import React from 'react';
import { FileText, BarChart3, Clock, Coins } from 'lucide-react';
import { Card } from '../ui/Card';

interface StatCardProps {
  title: string;
  value: string | number;
  description: string;
  icon: React.ReactNode;
  color: 'primary' | 'success' | 'warning' | 'error';
}

const StatCard: React.FC<StatCardProps> = ({ title, value, description, icon, color }) => {
  const colorClasses = {
    primary: 'bg-primary-100 text-primary-600',
    success: 'bg-success-100 text-success-600',
    warning: 'bg-warning-100 text-warning-600',
    error: 'bg-error-100 text-error-600'
  };

  return (
    <Card className="hover:shadow-medium transition-shadow duration-200">
      <Card.Body>
        <div className="flex items-center">
          <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
            {icon}
          </div>
          <div className="ml-4 flex-1">
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            <p className="text-xs text-gray-500">{description}</p>
          </div>
        </div>
      </Card.Body>
    </Card>
  );
};

export const StatsCards: React.FC = () => {
  // Mock data - in a real app, this would come from session state or API
  const stats = [
    {
      title: 'Documents Uploaded',
      value: 0,
      description: 'In current session',
      icon: <FileText className="w-6 h-6" />,
      color: 'primary' as const
    },
    {
      title: 'Analyses Completed',
      value: 0,
      description: 'Ready for review',
      icon: <BarChart3 className="w-6 h-6" />,
      color: 'success' as const
    },
    {
      title: 'Tokens Remaining',
      value: '2,500',
      description: 'Available for analysis',
      icon: <Coins className="w-6 h-6" />,
      color: 'primary' as const
    },
    {
      title: 'Processing Status',
      value: 'Ready',
      description: 'System status',
      icon: <Clock className="w-6 h-6" />,
      color: 'success' as const
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <StatCard
          key={index}
          title={stat.title}
          value={stat.value}
          description={stat.description}
          icon={stat.icon}
          color={stat.color}
        />
      ))}
    </div>
  );
};
